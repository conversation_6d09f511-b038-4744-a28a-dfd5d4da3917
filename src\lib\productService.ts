import { database } from './firebase';
import { ref as dbRef, push, set, get, remove, update } from 'firebase/database';
import { uploadToCloudinary, deleteFromCloudinary } from './cloudinary';

export interface Product {
  id?: string;
  name: string;
  caption: string;
  imageUrl: string;
  publicId?: string;
  createdAt?: number;
  updatedAt?: number;
}

export class ProductService {
  private productsRef = dbRef(database, 'products');

  // Get all products
  async getAllProducts(): Promise<Product[]> {
    try {
      const snapshot = await get(this.productsRef);
      if (snapshot.exists()) {
        const data = snapshot.val();
        const products: Product[] = [];
        
        Object.keys(data).forEach(key => {
          products.push({
            id: key,
            ...data[key]
          });
        });

        return products;
      }
      return [];
    } catch (error) {
      console.error('Error fetching products:', error);
      throw new Error('Failed to fetch products');
    }
  }

  // Get paginated products
  async getPaginatedProducts(page: number, pageSize: number): Promise<{ products: Product[], total: number }> {
    try {
      // Get all products first (we need this to sort by updatedAt)
      const snapshot = await get(this.productsRef);
      if (snapshot.exists()) {
        const data = snapshot.val();
        let products: Product[] = [];
        
        Object.keys(data).forEach(key => {
          products.push({
            id: key,
            ...data[key]
          });
        });

        // Sort by updatedAt (newest first)
        products = products.sort((a, b) => {
          const aTime = a.updatedAt || a.createdAt || 0;
          const bTime = b.updatedAt || b.createdAt || 0;
          return bTime - aTime;
        });

        // Calculate pagination
        const total = products.length;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const paginatedProducts = products.slice(start, end);

        return {
          products: paginatedProducts,
          total
        };
      }
      return {
        products: [],
        total: 0
      };
    } catch (error) {
      console.error('Error fetching products:', error);
      throw new Error('Failed to fetch products');
    }
  }

  // Get a single product by ID
  async getProductById(id: string): Promise<Product | null> {
    try {
      const productRef = dbRef(database, `products/${id}`);
      const snapshot = await get(productRef);
      
      if (snapshot.exists()) {
        return {
          id,
          ...snapshot.val()
        };
      }
      return null;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw new Error('Failed to fetch product');
    }
  }

  // Add a new product
  async addProduct(productData: { name: string; caption: string }, imageFile: File): Promise<string> {
    try {
      // Upload image to Cloudinary
      const { url: imageUrl, publicId } = await uploadToCloudinary(imageFile);
      const timestamp = Date.now();

      // Prepare product data
      const newProduct: Omit<Product, 'id'> = {
        ...productData,
        imageUrl,
        publicId,
        createdAt: timestamp,
        updatedAt: timestamp
      };

      // Add to Realtime Database
      const newProductRef = push(this.productsRef);
      await set(newProductRef, newProduct);

      return newProductRef.key!;
    } catch (error) {
      console.error('Error adding product:', error);
      throw new Error('Failed to add product');
    }
  }

  // Update an existing product
  async updateProduct(id: string, productData: Partial<Product>, imageFile?: File): Promise<void> {
    try {
      const productRef = dbRef(database, `products/${id}`);
      const currentProduct = await this.getProductById(id);
      
      if (!currentProduct) {
        throw new Error('Product not found');
      }

      let imageUrl = currentProduct.imageUrl;
      let publicId = currentProduct.publicId;

      // If a new image is provided, upload it and delete the old one
      if (imageFile) {
        // Upload new image to Cloudinary
        const { url: newImageUrl, publicId: newPublicId } = await uploadToCloudinary(imageFile);
        imageUrl = newImageUrl;
        
        // Delete old image if it exists
        if (currentProduct.publicId) {
          try {
            await deleteFromCloudinary(currentProduct.publicId);
          } catch (error) {
            console.error('Error deleting old image:', error);
          }
        }

        publicId = newPublicId;
      }

      // Update product data
      const updatedProduct = {
        ...currentProduct,
        ...productData,
        imageUrl,
        publicId,
        updatedAt: Date.now()
      };

      await update(productRef, updatedProduct);
    } catch (error) {
      console.error('Error updating product:', error);
      throw new Error('Failed to update product');
    }
  }

  // Delete a product
  async deleteProduct(id: string): Promise<void> {
    try {
      const product = await this.getProductById(id);
      
      if (!product) {
        throw new Error('Product not found');
      }

      // Delete image from Cloudinary
      if (product.publicId) {
        try {
          await deleteFromCloudinary(product.publicId);
        } catch (error) {
          console.error('Error deleting image from Cloudinary:', error);
        }
      }

      // Delete product from Realtime Database
      const productRef = dbRef(database, `products/${id}`);
      await remove(productRef);
    } catch (error) {
      console.error('Error deleting product:', error);
      throw new Error('Failed to delete product');
    }
  }

  // Search products by name or caption
  async searchProducts(query: string): Promise<Product[]> {
    try {
      const allProducts = await this.getAllProducts();
      const searchTerm = query.toLowerCase();
      
      return allProducts.filter(product => 
        product.name.toLowerCase().includes(searchTerm) ||
        product.caption.toLowerCase().includes(searchTerm)
      );
    } catch (error) {
      console.error('Error searching products:', error);
      throw new Error('Failed to search products');
    }
  }
}

// Export a singleton instance
export const productService = new ProductService();
